# Use OpenJDK 17 as base image
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Copy Maven wrapper and pom.xml files
COPY .mvn/ .mvn/
COPY mvnw pom.xml ./

# Copy parent pom.xml from root
COPY ../pom.xml ../pom.xml

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN ./mvnw dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN ./mvnw clean package -DskipTests

# Expose port
EXPOSE 8081

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8081/users/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "target/user-service-1.0.0-SNAPSHOT.jar"]

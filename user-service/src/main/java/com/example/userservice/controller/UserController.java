package com.example.userservice.controller;

import com.example.userservice.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * User Controller
 * 
 * Provides REST endpoints for user-related operations.
 * This controller demonstrates:
 * - Basic CRUD operations
 * - Authentication endpoint that expects headers from the gateway
 * - Logging for debugging purposes
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@RestController
@RequestMapping("/users")
@Slf4j
public class UserController {

    /**
     * Mock user database for demonstration
     */
    private static final Map<Long, User> USERS = new HashMap<>();
    
    static {
        // Initialize with some mock users
        USERS.put(1L, new User(1L, "John Doe", "<EMAIL>", "USER"));
        USERS.put(2L, new User(2L, "<PERSON>", "<EMAIL>", "ADMIN"));
        USERS.put(3L, new User(3L, "<PERSON>", "<EMAIL>", "USER"));
        USERS.put(123L, new User(123L, "Gateway User", "<EMAIL>", "USER"));
    }

    /**
     * Get user by ID
     * 
     * @param id the user ID
     * @return the user information or 404 if not found
     */
    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        log.info("Received request to get user with ID: {}", id);
        
        User user = USERS.get(id);
        if (user != null) {
            log.info("Found user: {}", user.getName());
            return ResponseEntity.ok(user);
        } else {
            log.warn("User not found with ID: {}", id);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Protected authentication endpoint
     * 
     * This endpoint expects the X-User-Id header to be set by the API Gateway
     * after successful authentication. It demonstrates how the gateway can
     * modify requests before forwarding them to downstream services.
     * 
     * @param userIdHeader the user ID header set by the gateway
     * @param allHeaders all request headers for debugging
     * @return authentication success message
     */
    @PostMapping("/auth")
    public ResponseEntity<Map<String, String>> authenticatedAccess(
            @RequestHeader(value = "X-User-Id", required = false) String userIdHeader,
            @RequestHeader Map<String, String> allHeaders) {
        
        log.info("Received authenticated request");
        log.info("X-User-Id header: {}", userIdHeader);
        log.info("All headers: {}", allHeaders);
        
        Map<String, String> response = new HashMap<>();
        
        if (userIdHeader != null) {
            response.put("message", "Authenticated access for user: " + userIdHeader);
            response.put("status", "success");
            response.put("userId", userIdHeader);
            log.info("Successfully processed authenticated request for user: {}", userIdHeader);
        } else {
            response.put("message", "No user ID found in headers");
            response.put("status", "warning");
            log.warn("No X-User-Id header found in authenticated request");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Health check endpoint
     * 
     * @return service status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        status.put("service", "user-service");
        status.put("status", "UP");
        status.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return ResponseEntity.ok(status);
    }
}

package com.example.userservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * User Service Application
 * 
 * This microservice provides user-related functionality including:
 * - User information retrieval
 * - Authentication endpoints
 * - Integration with Eureka for service discovery
 * 
 * The service registers itself with Eureka server and can be discovered
 * by other services (like the API Gateway) for routing requests.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableEurekaClient
public class UserServiceApplication {

    /**
     * Main method to start the User Service
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}

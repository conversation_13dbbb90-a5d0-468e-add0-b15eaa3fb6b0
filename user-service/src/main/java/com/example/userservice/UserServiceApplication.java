package com.example.userservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * User Service Application
 * 
 * This microservice provides user-related functionality including:
 * - User information retrieval
 * - Authentication endpoints
 * - Integration with Nacos for service discovery
 * 
 * The service registers itself with Nacos server and can be discovered
 * by other services (like the API Gateway) for routing requests.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableDiscoveryClient
public class UserServiceApplication {

    /**
     * Main method to start the User Service
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}

package com.example.userservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * User Model
 * 
 * Represents a user entity with basic information.
 * This is a simple POJO used for demonstration purposes.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    
    /**
     * Unique identifier for the user
     */
    private Long id;
    
    /**
     * User's full name
     */
    private String name;
    
    /**
     * User's email address
     */
    private String email;
    
    /**
     * User's role (for demonstration purposes)
     */
    private String role;
}

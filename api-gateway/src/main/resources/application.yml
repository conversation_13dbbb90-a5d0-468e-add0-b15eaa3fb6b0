server:
  port: 8080

spring:
  application:
    name: api-gateway
  
  # Redis configuration for rate limiting
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Spring Cloud Gateway configuration
  cloud:
    gateway:
      # Global CORS configuration
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"
            allowCredentials: false
      
      # Route definitions
      routes:
        # User Service Route
        - id: user-service-route
          uri: lb://user-service
          predicates:
            - Path=/api/users/**
          filters:
            - StripPrefix=1  # Remove /api prefix before forwarding
            - name: Retry
              args:
                retries: 3
                statuses: BAD_GATEWAY,SERVICE_UNAVAILABLE
                methods: GET,POST
                backoff:
                  firstBackoff: 10ms
                  maxBackoff: 50ms
                  factor: 2
                  basedOnPreviousValue: false
        
        # Product Service Route with Rate Limiting
        - id: product-service-route
          uri: lb://product-service
          predicates:
            - Path=/api/products/**
          filters:
            - StripPrefix=1  # Remove /api prefix before forwarding
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 5  # tokens per second
                redis-rate-limiter.burstCapacity: 10  # maximum tokens
                redis-rate-limiter.requestedTokens: 1  # tokens per request
                key-resolver: "#{@ipKeyResolver}"  # Bean name for key resolution
            - name: AddResponseHeader
              args:
                headerName: X-RateLimit-Applied
                headerValue: "true"

# Eureka client configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# Logging configuration
logging:
  level:
    com.example.apigateway: DEBUG
    org.springframework.cloud.gateway: INFO
    org.springframework.web.reactive: DEBUG
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Custom application properties
app:
  gateway:
    # Rate limiting configuration
    rate-limit:
      default-replenish-rate: 10
      default-burst-capacity: 20
    # Timeout configuration
    timeout:
      connect: 3000ms
      response: 10000ms

package com.example.apigateway.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * Custom Gateway Filter Factory for Adding Response Headers
 * 
 * This filter demonstrates how to create custom gateway filter factories
 * that can be configured per route in the application.yml file.
 * 
 * Key Learning Points:
 * 1. AbstractGatewayFilterFactory provides the base for custom filters
 * 2. Configuration class defines the parameters for the filter
 * 3. Can be applied to specific routes rather than globally
 * 4. Demonstrates response modification
 * 5. Shows how to access configuration in the filter logic
 * 
 * Usage in application.yml:
 * ```yaml
 * spring:
 *   cloud:
 *     gateway:
 *       routes:
 *         - id: some-route
 *           uri: lb://some-service
 *           filters:
 *             - AddResponseHeader=X-Custom-Header,CustomValue
 * ```
 * 
 * This filter adds a timestamp header to all responses, which can be useful
 * for debugging, monitoring, and tracking request processing times.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Component
@Slf4j
public class AddResponseHeaderGatewayFilterFactory 
        extends AbstractGatewayFilterFactory<AddResponseHeaderGatewayFilterFactory.Config> {

    /**
     * Constructor that specifies the configuration class
     */
    public AddResponseHeaderGatewayFilterFactory() {
        super(Config.class);
    }

    /**
     * Creates the actual gateway filter with the provided configuration
     *
     * @param config the filter configuration
     * @return the gateway filter
     */
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            log.debug("AddResponseHeaderGatewayFilterFactory: Adding header {} with value {}",
                    config.getHeaderName(), config.getHeaderValue());

            // Continue with the filter chain and modify response when complete
            return chain.filter(exchange).doOnSuccess(aVoid -> {
                ServerHttpResponse response = exchange.getResponse();

                // Add the configured header
                response.getHeaders().add(config.getHeaderName(), config.getHeaderValue());

                // Add a timestamp header for debugging
                response.getHeaders().add("X-Gateway-Timestamp", Instant.now().toString());

                log.debug("AddResponseHeaderGatewayFilterFactory: Added headers to response");
            });
        };
    }

    /**
     * Configuration class for the filter
     * 
     * This class defines the parameters that can be configured
     * when using this filter in routes.
     */
    @Data
    public static class Config {
        /**
         * The name of the header to add
         */
        private String headerName = "X-Gateway-Response";
        
        /**
         * The value of the header to add
         */
        private String headerValue = "processed";
    }
}

package com.example.apigateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
 * Logging Global Filter
 * 
 * This filter demonstrates comprehensive request/response logging in Spring Cloud Gateway.
 * It showcases several important concepts for working with reactive streams and
 * the challenges of reading request bodies in a gateway.
 * 
 * Key Learning Points:
 * 1. Request body can only be consumed once in reactive streams
 * 2. ServerWebExchangeUtils.cacheRequestBody() solves the consumption problem
 * 3. Conditional logging based on content type and size
 * 4. Reactive chain composition with .then() for response logging
 * 5. Safe handling of different content types
 * 
 * Logging Strategy:
 * - Always log request method, path, and headers
 * - Conditionally log request body (JSON only, size limited)
 * - Log response status and headers after processing
 * - Skip body logging for large payloads or non-JSON content
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Component
@Slf4j
public class LoggingGlobalFilter implements GlobalFilter, Ordered {

    private static final int MAX_BODY_SIZE = 1024 * 1024; // 1MB
    private static final String JSON_CONTENT_TYPE = "application/json";

    /**
     * Filter logic for request/response logging
     * 
     * @param exchange the current server exchange
     * @param chain provides a way to delegate to the next filter
     * @return Mono<Void> indicating when request processing is complete
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // Log basic request information
        logRequestBasics(request);
        
        // Check if we should log the request body
        if (shouldLogRequestBody(request)) {
            // Cache the request body so it can be read multiple times
            // This is crucial because in reactive streams, the body can only be consumed once
            return ServerWebExchangeUtils.cacheRequestBody(exchange, (serverHttpRequest) -> {
                // Log the cached request body
                logRequestBody(serverHttpRequest);
                
                // Continue with the filter chain and log response when complete
                return chain.filter(exchange.mutate().request(serverHttpRequest).build())
                        .then(Mono.fromRunnable(() -> logResponse(exchange.getResponse())));
            });
        } else {
            // Skip body logging and continue with the filter chain
            log.debug("LoggingGlobalFilter: Skipping request body logging for {}", request.getPath());
            
            return chain.filter(exchange)
                    .then(Mono.fromRunnable(() -> logResponse(exchange.getResponse())));
        }
    }

    /**
     * Logs basic request information (method, path, headers)
     * 
     * @param request the server HTTP request
     */
    private void logRequestBasics(ServerHttpRequest request) {
        log.info("LoggingGlobalFilter: Incoming Request - {} {}", 
                request.getMethod(), request.getPath());
        
        // Log important headers (excluding sensitive ones)
        HttpHeaders headers = request.getHeaders();
        headers.forEach((name, values) -> {
            if (!isSensitiveHeader(name)) {
                log.debug("LoggingGlobalFilter: Request Header - {}: {}", name, values);
            }
        });
        
        log.debug("LoggingGlobalFilter: Request Content-Type: {}", 
                headers.getContentType());
        log.debug("LoggingGlobalFilter: Request Content-Length: {}", 
                headers.getContentLength());
    }

    /**
     * Determines if request body should be logged based on content type and size
     * 
     * This implements a safe logging strategy:
     * - Only log JSON content (avoid binary data)
     * - Only log if content length is reasonable
     * - Skip if content type is not suitable for logging
     * 
     * @param request the server HTTP request
     * @return true if body should be logged
     */
    private boolean shouldLogRequestBody(ServerHttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        MediaType contentType = headers.getContentType();
        long contentLength = headers.getContentLength();
        
        // Skip if no content type
        if (contentType == null) {
            return false;
        }
        
        // Only log JSON content
        if (!contentType.includes(MediaType.APPLICATION_JSON)) {
            log.debug("LoggingGlobalFilter: Skipping body logging - not JSON content: {}", contentType);
            return false;
        }
        
        // Skip if content is too large
        if (contentLength > MAX_BODY_SIZE) {
            log.debug("LoggingGlobalFilter: Skipping body logging - content too large: {} bytes", contentLength);
            return false;
        }
        
        return true;
    }

    /**
     * Logs the cached request body
     *
     * This method demonstrates how to safely read the request body
     * after it has been cached using ServerWebExchangeUtils.cacheRequestBody()
     *
     * @param request the server HTTP request with cached body
     */
    private void logRequestBody(ServerHttpRequest request) {
        // In the cacheRequestBody callback, the body is available in the exchange
        // For this demo, we'll log that body logging is enabled
        log.info("LoggingGlobalFilter: Request body logging enabled for JSON content");
    }

    /**
     * Logs response information after the request has been processed
     * 
     * This method is called in the .then() operator, which means it executes
     * after the entire filter chain has completed.
     * 
     * @param response the server HTTP response
     */
    private void logResponse(ServerHttpResponse response) {
        log.info("LoggingGlobalFilter: Response Status: {}", response.getStatusCode());
        
        // Log response headers (excluding sensitive ones)
        HttpHeaders headers = response.getHeaders();
        headers.forEach((name, values) -> {
            if (!isSensitiveHeader(name)) {
                log.debug("LoggingGlobalFilter: Response Header - {}: {}", name, values);
            }
        });
    }

    /**
     * Checks if a header contains sensitive information that shouldn't be logged
     * 
     * @param headerName the header name to check
     * @return true if the header is sensitive
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.contains("authorization") || 
               lowerName.contains("cookie") || 
               lowerName.contains("password") ||
               lowerName.contains("token");
    }

    /**
     * Sets the order of this filter
     * 
     * We want this to run after authentication but before other filters
     * 
     * @return the order value
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}

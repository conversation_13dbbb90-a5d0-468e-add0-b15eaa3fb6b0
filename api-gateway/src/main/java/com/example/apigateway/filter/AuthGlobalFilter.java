package com.example.apigateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * Authentication Global Filter
 * 
 * This filter demonstrates authentication in a Spring Cloud Gateway.
 * It runs with the highest precedence to ensure authentication happens
 * before any other processing.
 * 
 * Key Learning Points:
 * 1. Global filters run for all routes unless specifically excluded
 * 2. Reactive programming model - returns Mono<Void> instead of blocking
 * 3. Request modification - adding headers for downstream services
 * 4. Early termination - returning error responses without calling downstream
 * 5. Context passing - no ThreadLocal, context flows through the reactive chain
 * 
 * Authentication Logic:
 * - Checks for "Authorization" header
 * - Validates the token (mock validation for demo)
 * - Extracts user information from token
 * - Adds X-User-Id header for downstream services
 * - Returns 401 for invalid/missing tokens
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Component
@Slf4j
public class AuthGlobalFilter implements GlobalFilter, Ordered {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String USER_ID_HEADER = "X-User-Id";
    private static final String VALID_TOKEN = "Bearer valid-token";
    private static final String EXTRACTED_USER_ID = "123";

    /**
     * Filter logic for authentication
     * 
     * @param exchange the current server exchange
     * @param chain provides a way to delegate to the next filter
     * @return Mono<Void> indicating when request processing is complete
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().toString();
        
        log.info("AuthGlobalFilter: Processing request to {}", path);
        
        // Skip authentication for health check endpoints
        if (path.contains("/health") || path.contains("/actuator")) {
            log.debug("AuthGlobalFilter: Skipping authentication for health check: {}", path);
            return chain.filter(exchange);
        }
        
        // Get Authorization header
        String authHeader = request.getHeaders().getFirst(AUTHORIZATION_HEADER);
        log.debug("AuthGlobalFilter: Authorization header: {}", authHeader);
        
        // Validate token
        if (!isValidToken(authHeader)) {
            log.warn("AuthGlobalFilter: Invalid or missing authorization token for path: {}", path);
            return handleUnauthorized(exchange);
        }
        
        // Extract user ID from token (mock implementation)
        String userId = extractUserIdFromToken(authHeader);
        log.info("AuthGlobalFilter: Successfully authenticated user: {}", userId);
        
        // Add user ID to request headers for downstream services
        // This demonstrates how the gateway can modify requests before forwarding
        ServerHttpRequest modifiedRequest = request.mutate()
                .header(USER_ID_HEADER, userId)
                .build();
        
        // Create new exchange with modified request
        ServerWebExchange modifiedExchange = exchange.mutate()
                .request(modifiedRequest)
                .build();
        
        log.debug("AuthGlobalFilter: Added {} header with value: {}", USER_ID_HEADER, userId);
        
        // Continue with the filter chain
        return chain.filter(modifiedExchange);
    }

    /**
     * Validates the authorization token
     * 
     * In a real application, this would:
     * - Parse JWT tokens
     * - Validate signatures
     * - Check expiration
     * - Verify against user database
     * 
     * @param authHeader the authorization header value
     * @return true if token is valid
     */
    private boolean isValidToken(String authHeader) {
        // Mock validation - in real app, implement proper JWT validation
        return VALID_TOKEN.equals(authHeader);
    }

    /**
     * Extracts user ID from the authorization token
     * 
     * In a real application, this would parse the JWT payload
     * and extract user information.
     * 
     * @param authHeader the authorization header value
     * @return the extracted user ID
     */
    private String extractUserIdFromToken(String authHeader) {
        // Mock extraction - in real app, decode JWT and extract user info
        return EXTRACTED_USER_ID;
    }

    /**
     * Handles unauthorized requests by returning 401 status
     * 
     * This demonstrates how to terminate request processing early
     * and return custom error responses.
     * 
     * @param exchange the current server exchange
     * @return Mono<Void> with 401 response
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        
        // Add custom headers to the error response
        response.getHeaders().add("Content-Type", "application/json");
        response.getHeaders().add("X-Error-Reason", "Invalid or missing authorization token");
        
        // In a real application, you might want to return a JSON error body
        // For simplicity, we're just returning the status code
        return response.setComplete();
    }

    /**
     * Sets the order of this filter
     * 
     * HIGHEST_PRECEDENCE ensures this filter runs first,
     * which is important for authentication.
     * 
     * @return the order value
     */
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}

package com.example.apigateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * Gateway Configuration
 * 
 * This configuration class provides beans for various gateway functionalities,
 * particularly for rate limiting and request resolution.
 * 
 * Key Learning Points:
 * 1. KeyResolver determines how to identify unique clients for rate limiting
 * 2. Different strategies: IP-based, user-based, header-based
 * 3. Reactive programming with Mono<String>
 * 4. Fallback strategies for missing information
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Configuration
@Slf4j
public class GatewayConfig {

    /**
     * IP-based Key Resolver for Rate Limiting
     * 
     * This resolver uses the client's IP address to identify unique clients
     * for rate limiting purposes. Each IP address gets its own rate limit bucket.
     * 
     * In production, you might want to consider:
     * - X-Forwarded-For header for clients behind proxies
     * - X-Real-IP header for load balancer scenarios
     * - Combining IP with other identifiers for more granular control
     * 
     * @return KeyResolver that resolves by IP address
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return new KeyResolver() {
            @Override
            public Mono<String> resolve(ServerWebExchange exchange) {
                // Get the remote address from the request
                String clientIp = Objects.requireNonNull(
                    exchange.getRequest().getRemoteAddress()
                ).getAddress().getHostAddress();
                
                log.debug("Rate limiting key resolved to IP: {}", clientIp);
                return Mono.just(clientIp);
            }
        };
    }

    /**
     * User-based Key Resolver for Rate Limiting
     * 
     * This resolver uses the authenticated user ID for rate limiting.
     * It looks for the X-User-Id header that should be set by the
     * authentication filter.
     * 
     * This provides more accurate rate limiting per user rather than per IP,
     * which is useful when multiple users share the same IP (corporate networks).
     * 
     * @return KeyResolver that resolves by user ID
     */
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            
            if (userId != null) {
                log.debug("Rate limiting key resolved to user: {}", userId);
                return Mono.just(userId);
            } else {
                // Fallback to IP if no user ID is available
                String clientIp = Objects.requireNonNull(
                    exchange.getRequest().getRemoteAddress()
                ).getAddress().getHostAddress();
                
                log.debug("No user ID found, falling back to IP: {}", clientIp);
                return Mono.just("anonymous:" + clientIp);
            }
        };
    }

    /**
     * Path-based Key Resolver for Rate Limiting
     * 
     * This resolver creates different rate limit buckets for different API paths.
     * This allows for different rate limits on different endpoints.
     * 
     * For example:
     * - /api/users/** might have a higher rate limit
     * - /api/products/** might have a lower rate limit
     * - /api/admin/** might have very strict limits
     * 
     * @return KeyResolver that resolves by request path
     */
    @Bean
    public KeyResolver pathKeyResolver() {
        return exchange -> {
            String path = exchange.getRequest().getPath().toString();
            String clientIp = Objects.requireNonNull(
                exchange.getRequest().getRemoteAddress()
            ).getAddress().getHostAddress();
            
            // Combine IP and path for unique identification
            String key = clientIp + ":" + path;
            
            log.debug("Rate limiting key resolved to path-based: {}", key);
            return Mono.just(key);
        };
    }

    /**
     * Custom Header-based Key Resolver
     * 
     * This resolver uses a custom header (like API key or client ID)
     * for rate limiting. This is useful for API management scenarios
     * where each client has a unique identifier.
     * 
     * @return KeyResolver that resolves by custom header
     */
    @Bean
    public KeyResolver headerKeyResolver() {
        return exchange -> {
            // Look for custom client identifier header
            String clientId = exchange.getRequest().getHeaders().getFirst("X-Client-ID");
            String apiKey = exchange.getRequest().getHeaders().getFirst("X-API-Key");
            
            if (clientId != null) {
                log.debug("Rate limiting key resolved to client ID: {}", clientId);
                return Mono.just("client:" + clientId);
            } else if (apiKey != null) {
                log.debug("Rate limiting key resolved to API key: {}", apiKey.substring(0, 8) + "...");
                return Mono.just("api:" + apiKey);
            } else {
                // Fallback to IP
                String clientIp = Objects.requireNonNull(
                    exchange.getRequest().getRemoteAddress()
                ).getAddress().getHostAddress();
                
                log.debug("No client identifier found, falling back to IP: {}", clientIp);
                return Mono.just("unknown:" + clientIp);
            }
        };
    }
}

package com.example.apigateway.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Global Exception Handler for JSON Error Responses
 * 
 * This handler provides consistent JSON error responses for all unhandled
 * exceptions in the Spring Cloud Gateway. It replaces the default HTML
 * error pages with structured JSON responses.
 * 
 * Key Learning Points:
 * 1. ErrorWebExceptionHandler provides global exception handling
 * 2. @Order annotation ensures this handler runs before default handlers
 * 3. Reactive error handling with Mono<Void>
 * 4. Custom JSON error response format
 * 5. Different handling for different exception types
 * 6. Proper HTTP status code mapping
 * 
 * Error Response Format:
 * ```json
 * {
 *   "timestamp": "2023-12-01T10:30:00Z",
 *   "path": "/api/users/123",
 *   "status": 500,
 *   "error": "Internal Server Error",
 *   "message": "Connection refused",
 *   "requestId": "abc-123-def"
 * }
 * ```
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Component
@Order(-1) // Higher precedence than default error handler
@RequiredArgsConstructor
@Slf4j
public class JsonGlobalExceptionHandler implements ErrorWebExceptionHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Handles all unhandled exceptions and returns JSON error responses
     * 
     * @param exchange the current server exchange
     * @param ex the exception that occurred
     * @return Mono<Void> indicating when error handling is complete
     */
    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        // Determine HTTP status based on exception type
        HttpStatus status = determineHttpStatus(ex);
        
        // Create error response
        Map<String, Object> errorResponse = createErrorResponse(exchange, ex, status);
        
        // Log the error
        logError(exchange, ex, status);
        
        // Set response properties
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        
        // Convert error response to JSON and write to response
        try {
            String jsonResponse = objectMapper.writeValueAsString(errorResponse);
            DataBufferFactory bufferFactory = response.bufferFactory();
            DataBuffer buffer = bufferFactory.wrap(jsonResponse.getBytes());
            
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException jsonEx) {
            log.error("Failed to serialize error response to JSON", jsonEx);
            return response.setComplete();
        }
    }

    /**
     * Determines the appropriate HTTP status code based on the exception type
     * 
     * @param ex the exception
     * @return the HTTP status code
     */
    private HttpStatus determineHttpStatus(Throwable ex) {
        if (ex instanceof ResponseStatusException) {
            return ((ResponseStatusException) ex).getStatus();
        }
        
        // Map common exceptions to appropriate status codes
        String exceptionName = ex.getClass().getSimpleName();
        switch (exceptionName) {
            case "ConnectException":
            case "ConnectTimeoutException":
                return HttpStatus.SERVICE_UNAVAILABLE;
            case "TimeoutException":
            case "ReadTimeoutException":
                return HttpStatus.GATEWAY_TIMEOUT;
            case "NotFoundException":
                return HttpStatus.NOT_FOUND;
            case "UnauthorizedException":
                return HttpStatus.UNAUTHORIZED;
            case "ForbiddenException":
                return HttpStatus.FORBIDDEN;
            case "BadRequestException":
                return HttpStatus.BAD_REQUEST;
            default:
                return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }

    /**
     * Creates a structured error response map
     * 
     * @param exchange the current server exchange
     * @param ex the exception
     * @param status the HTTP status
     * @return the error response map
     */
    private Map<String, Object> createErrorResponse(ServerWebExchange exchange, Throwable ex, HttpStatus status) {
        Map<String, Object> errorResponse = new HashMap<>();
        
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("path", exchange.getRequest().getPath().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", getErrorMessage(ex));
        
        // Add request ID if available (useful for tracing)
        String requestId = exchange.getRequest().getHeaders().getFirst("X-Request-ID");
        if (requestId != null) {
            errorResponse.put("requestId", requestId);
        }
        
        // Add additional context for specific error types
        if (status == HttpStatus.SERVICE_UNAVAILABLE) {
            errorResponse.put("details", "Downstream service is currently unavailable");
        } else if (status == HttpStatus.GATEWAY_TIMEOUT) {
            errorResponse.put("details", "Request timeout while processing");
        }
        
        return errorResponse;
    }

    /**
     * Extracts a meaningful error message from the exception
     * 
     * @param ex the exception
     * @return the error message
     */
    private String getErrorMessage(Throwable ex) {
        if (ex.getMessage() != null && !ex.getMessage().isEmpty()) {
            return ex.getMessage();
        }
        
        // Provide default messages for common exceptions
        String exceptionName = ex.getClass().getSimpleName();
        switch (exceptionName) {
            case "ConnectException":
                return "Unable to connect to downstream service";
            case "ConnectTimeoutException":
                return "Connection timeout to downstream service";
            case "TimeoutException":
            case "ReadTimeoutException":
                return "Request processing timeout";
            default:
                return "An unexpected error occurred";
        }
    }

    /**
     * Logs error information for monitoring and debugging
     * 
     * @param exchange the current server exchange
     * @param ex the exception
     * @param status the HTTP status
     */
    private void logError(ServerWebExchange exchange, Throwable ex, HttpStatus status) {
        String path = exchange.getRequest().getPath().toString();
        String method = exchange.getRequest().getMethod().toString();
        
        if (status.is5xxServerError()) {
            log.error("Server error occurred - {} {} - Status: {} - Exception: {}", 
                    method, path, status.value(), ex.getMessage(), ex);
        } else if (status.is4xxClientError()) {
            log.warn("Client error occurred - {} {} - Status: {} - Exception: {}", 
                    method, path, status.value(), ex.getMessage());
        } else {
            log.info("Error occurred - {} {} - Status: {} - Exception: {}", 
                    method, path, status.value(), ex.getMessage());
        }
    }
}

package com.example.apigateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * API Gateway Application
 * 
 * This is the main entry point for the Spring Cloud Gateway application.
 * The gateway serves as a single entry point for all client requests and
 * routes them to appropriate microservices.
 * 
 * Key Features Demonstrated:
 * - Service Discovery Integration with Eureka
 * - Custom Authentication Filter
 * - Request/Response Logging
 * - Rate Limiting with Redis
 * - Global Error Handling
 * - Custom Gateway Filter Factories
 * 
 * The gateway uses reactive programming model (WebFlux) which allows
 * for non-blocking I/O operations and better scalability.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableEurekaClient
public class ApiGatewayApplication {

    /**
     * Main method to start the API Gateway
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(ApiGatewayApplication.class, args);
    }
}

package com.example.productservice.controller;

import com.example.productservice.model.Product;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Product Controller
 * 
 * Provides REST endpoints for product-related operations.
 * This controller demonstrates:
 * - Product information retrieval
 * - Rate limiting scenarios (will be limited by the gateway)
 * - Logging for debugging purposes
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@RestController
@RequestMapping("/products")
@Slf4j
public class ProductController {

    /**
     * Mock product database for demonstration
     */
    private static final Map<Long, Product> PRODUCTS = new HashMap<>();
    
    static {
        // Initialize with some mock products
        PRODUCTS.put(1L, new Product(1L, "Awesome Gadget", "A really cool gadget that does amazing things", 
                new BigDecimal("99.99"), "Electronics", 50));
        PRODUCTS.put(2L, new Product(2L, "Super Widget", "The best widget you'll ever use", 
                new BigDecimal("149.99"), "Tools", 25));
        PRODUCTS.put(3L, new Product(3L, "Magic Device", "A device that works like magic", 
                new BigDecimal("299.99"), "Electronics", 10));
        PRODUCTS.put(4L, new Product(4L, "Ultimate Tool", "The ultimate tool for all your needs", 
                new BigDecimal("199.99"), "Tools", 30));
        PRODUCTS.put(5L, new Product(5L, "Smart Accessory", "A smart accessory for modern life", 
                new BigDecimal("79.99"), "Accessories", 100));
    }

    /**
     * Get product by ID
     * 
     * This endpoint will be used to demonstrate rate limiting.
     * The API Gateway will limit requests to this endpoint.
     * 
     * @param id the product ID
     * @return the product information or 404 if not found
     */
    @GetMapping("/{id}")
    public ResponseEntity<Product> getProduct(@PathVariable Long id) {
        log.info("Received request to get product with ID: {}", id);
        
        // Simulate some processing time
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Product product = PRODUCTS.get(id);
        if (product != null) {
            log.info("Found product: {}", product.getName());
            return ResponseEntity.ok(product);
        } else {
            log.warn("Product not found with ID: {}", id);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Get all products
     * 
     * @return list of all products
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllProducts() {
        log.info("Received request to get all products");
        
        Map<String, Object> response = new HashMap<>();
        response.put("products", PRODUCTS.values());
        response.put("total", PRODUCTS.size());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }

    /**
     * Health check endpoint
     * 
     * @return service status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> status = new HashMap<>();
        status.put("service", "product-service");
        status.put("status", "UP");
        status.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return ResponseEntity.ok(status);
    }
}

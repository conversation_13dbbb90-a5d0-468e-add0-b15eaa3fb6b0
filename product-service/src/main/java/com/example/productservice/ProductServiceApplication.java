package com.example.productservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Product Service Application
 * 
 * This microservice provides product-related functionality including:
 * - Product information retrieval
 * - Product catalog management
 * - Integration with Nacos for service discovery
 *
 * The service registers itself with Nacos server and can be discovered
 * by other services (like the API Gateway) for routing requests.
 * This service will be used to demonstrate rate limiting functionality.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableDiscoveryClient
public class ProductServiceApplication {

    /**
     * Main method to start the Product Service
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(ProductServiceApplication.class, args);
    }
}

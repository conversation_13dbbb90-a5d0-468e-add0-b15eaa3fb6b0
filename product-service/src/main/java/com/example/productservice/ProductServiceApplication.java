package com.example.productservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * Product Service Application
 * 
 * This microservice provides product-related functionality including:
 * - Product information retrieval
 * - Product catalog management
 * - Integration with Eureka for service discovery
 * 
 * The service registers itself with Eureka server and can be discovered
 * by other services (like the API Gateway) for routing requests.
 * This service will be used to demonstrate rate limiting functionality.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableEurekaClient
public class ProductServiceApplication {

    /**
     * Main method to start the Product Service
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(ProductServiceApplication.class, args);
    }
}

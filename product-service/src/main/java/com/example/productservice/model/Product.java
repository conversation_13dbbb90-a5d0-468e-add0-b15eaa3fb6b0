package com.example.productservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Product Model
 * 
 * Represents a product entity with basic information.
 * This is a simple POJO used for demonstration purposes.
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    
    /**
     * Unique identifier for the product
     */
    private Long id;
    
    /**
     * Product name
     */
    private String name;
    
    /**
     * Product description
     */
    private String description;
    
    /**
     * Product price
     */
    private BigDecimal price;
    
    /**
     * Product category
     */
    private String category;
    
    /**
     * Stock quantity
     */
    private Integer stock;
}

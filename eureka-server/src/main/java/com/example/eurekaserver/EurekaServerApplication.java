package com.example.eurekaserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.server.EnableEurekaServer;

/**
 * Eureka Server Application
 * 
 * This is the service discovery server that allows microservices to register themselves
 * and discover other services. It provides a centralized registry for all services
 * in the microservices ecosystem.
 * 
 * Key Features:
 * - Service Registration: Services can register themselves with Eureka
 * - Service Discovery: Services can discover and communicate with other services
 * - Health Monitoring: Eureka monitors the health of registered services
 * - Load Balancing: Provides service instance information for client-side load balancing
 * 
 * <AUTHOR> Cloud Gateway Learning Project
 */
@SpringBootApplication
@EnableEurekaServer
public class EurekaServerApplication {

    /**
     * Main method to start the Eureka Server
     * 
     * @param args command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run(EurekaServerApplication.class, args);
    }
}

server:
  port: 8761

spring:
  application:
    name: eureka-server

eureka:
  instance:
    hostname: localhost
  client:
    # Disable self-registration as this is the Eureka server itself
    register-with-eureka: false
    fetch-registry: false
    service-url:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/
  server:
    # Disable self-preservation mode in development
    enable-self-preservation: false
    # Eviction interval for expired instances
    eviction-interval-timer-in-ms: 10000

logging:
  level:
    com.netflix.eureka: INFO
    com.netflix.discovery: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Spring Cloud Gateway Learning Project

A comprehensive learning project demonstrating Spring Cloud Gateway functionality with Nacos service discovery.

## 项目概述

这是一个完整的 Spring Cloud Gateway 学习项目，模拟真实的微服务生态系统，包含：

- **api-gateway**: 核心的 Spring Cloud Gateway 应用
- **user-service**: 提供用户信息的下游服务
- **product-service**: 提供产品详情的下游服务
- **Nacos**: 阿里巴巴开源的服务发现和配置管理平台

## 核心功能演示

### 1. 认证过滤器 (AuthGlobalFilter)
- 检查 Authorization 头
- 模拟 JWT 令牌验证
- 为下游服务添加 X-User-Id 头
- 未授权请求返回 401 状态

### 2. 请求/响应日志 (LoggingGlobalFilter)
- 记录请求方法、路径和头信息
- 安全的请求体日志记录（仅 JSON，大小限制）
- 响应状态码和头信息记录
- 演示响应式编程中的请求体缓存

### 3. 自定义响应头过滤器 (AddResponseHeaderGatewayFilterFactory)
- 为特定路由添加自定义响应头
- 添加网关时间戳头用于调试
- 演示如何创建可配置的过滤器工厂

### 4. 全局错误处理 (JsonGlobalExceptionHandler)
- 统一的 JSON 错误响应格式
- 不同异常类型的 HTTP 状态码映射
- 结构化错误信息，包含时间戳、路径、状态等

### 5. 速率限制
- 基于 Redis 的分布式速率限制
- 多种 KeyResolver 策略（IP、用户、路径、头信息）
- 产品服务路由演示速率限制功能

### 6. 内置过滤器演示
- **StripPrefix**: 移除路径前缀
- **Retry**: 失败请求重试机制
- **RequestRateLimiter**: Redis 速率限制器

## 项目结构

```
spring-cloud-gateway-learning/
├── pom.xml                          # 父 POM 文件

├── api-gateway/                     # API 网关模块
│   ├── src/main/java/
│   │   └── com/example/apigateway/
│   │       ├── ApiGatewayApplication.java
│   │       ├── filter/              # 自定义过滤器
│   │       ├── exception/           # 全局异常处理
│   │       └── config/              # 配置类
│   └── src/main/resources/
│       └── application.yml          # 网关配置
├── user-service/                    # 用户服务模块
├── product-service/                 # 产品服务模块


## 快速开始

### 前置要求

- Java 17 或更高版本
- Maven 3.6 或更高版本
- Nacos Server（用于服务发现和配置管理）
- Redis（用于速率限制）

### 本地开发运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd spring-cloud-gateway-learning
   ```

2. **启动 Nacos Server**

   下载并启动 Nacos：
   ```bash
   # 下载 Nacos
   wget https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.tar.gz
   tar -xzf nacos-server-2.3.0.tar.gz
   cd nacos/bin

   # 启动 Nacos（单机模式）
   sh startup.sh -m standalone
   ```

   或者使用 Docker：
   ```bash
   docker run -d -p 8848:8848 --name nacos-server \
     -e MODE=standalone \
     nacos/nacos-server:v2.3.0
   ```

3. **启动 Redis**
   ```bash
   # 使用 Docker
   docker run -d -p 6379:6379 redis:7-alpine

   # 或者本地安装的 Redis
   redis-server
   ```

4. **构建项目**
   ```bash
   mvn clean install
   ```

5. **按顺序启动服务**
   ```bash
   # 1. 启动 User Service
   cd user-service
   mvn spring-boot:run

   # 2. 启动 Product Service（新终端）
   cd product-service
   mvn spring-boot:run

   # 3. 启动 API Gateway（新终端）
   cd api-gateway
   mvn spring-boot:run
   ```

## 服务端口

- **Nacos Server**: http://localhost:8848 (用户名/密码: nacos/nacos)
- **User Service**: http://localhost:8081
- **Product Service**: http://localhost:8082
- **API Gateway**: http://localhost:8080
- **Redis**: localhost:6379

## 测试示例

### 1. 成功的用户服务请求

```bash
curl -X GET "http://localhost:8080/api/users/1" \
  -H "Authorization: Bearer valid-token"
```

预期响应：
```json
{
  "id": 1,
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "USER"
}
```

### 2. 未授权请求（演示认证过滤器）

```bash
curl -X GET "http://localhost:8080/api/users/1"
```

预期响应：HTTP 401 Unauthorized

### 3. 认证端点测试（演示请求头传递）

```bash
curl -X POST "http://localhost:8080/api/users/auth" \
  -H "Authorization: Bearer valid-token" \
  -H "Content-Type: application/json"
```

预期响应：
```json
{
  "message": "Authenticated access for user: 123",
  "status": "success",
  "userId": "123"
}
```

### 4. 产品服务请求（演示速率限制）

```bash
# 正常请求
curl -X GET "http://localhost:8080/api/products/1" \
  -H "Authorization: Bearer valid-token"

# 快速连续请求测试速率限制
for i in {1..15}; do
  curl -X GET "http://localhost:8080/api/products/1" \
    -H "Authorization: Bearer valid-token"
  echo "Request $i completed"
done
```

当达到速率限制时，将收到 HTTP 429 Too Many Requests 响应。

### 5. 不存在的端点（演示全局错误处理）

```bash
curl -X GET "http://localhost:8080/api/nonexistent" \
  -H "Authorization: Bearer valid-token"
```

预期响应：
```json
{
  "timestamp": "2023-12-01T10:30:00Z",
  "path": "/api/nonexistent",
  "status": 404,
  "error": "Not Found",
  "message": "No matching route found"
}
```

### 6. 健康检查端点

```bash
# 网关健康检查
curl http://localhost:8080/actuator/health

# 查看网关路由信息
curl http://localhost:8080/actuator/gateway/routes

# 各服务健康检查
curl http://localhost:8081/users/health
curl http://localhost:8082/products/health

# Nacos 控制台
# 访问 http://localhost:8848/nacos 查看服务注册情况
```

## 学习要点

### 1. 响应式编程模型
- Spring Cloud Gateway 基于 WebFlux，使用响应式编程
- 理解 Mono 和 Flux 的使用
- 非阻塞 I/O 操作

### 2. 过滤器链
- GlobalFilter vs GatewayFilter
- 过滤器执行顺序（Order 接口）
- 请求和响应的修改

### 3. 服务发现集成
- 与 Nacos 的集成
- 负载均衡（lb:// 协议）
- 服务健康检查

### 4. 错误处理
- 全局异常处理器
- 自定义错误响应格式
- 不同异常类型的处理策略

### 5. 速率限制
- Redis 分布式速率限制
- 不同的 KeyResolver 策略
- 速率限制配置和调优

## 监控和调试

### 查看 Nacos 控制台
访问 http://localhost:8848/nacos 查看服务注册情况（用户名/密码: nacos/nacos）

### 查看网关路由
```bash
curl http://localhost:8080/actuator/gateway/routes | jq
```

### 查看速率限制状态
```bash
# 连接到 Redis 查看速率限制键
redis-cli
> KEYS *
> GET request_rate_limiter.{your-ip}.timestamp
```

## 故障排除

### 常见问题

1. **服务无法注册到 Nacos**
   - 检查 Nacos Server 是否正常运行
   - 验证网络连接和端口配置（默认 8848）
   - 确认 Nacos 控制台可以正常访问

2. **速率限制不工作**
   - 确认 Redis 服务正常运行
   - 检查 Redis 连接配置

3. **认证过滤器问题**
   - 确认 Authorization 头格式正确
   - 检查过滤器执行顺序



### 日志级别调整

在 `application.yml` 中调整日志级别：
```yaml
logging:
  level:
    com.example: DEBUG
    org.springframework.cloud.gateway: DEBUG
```

## 扩展建议

1. **添加 JWT 认证**：实现真实的 JWT 令牌验证
2. **集成 Spring Security**：添加更完整的安全机制
3. **添加分布式追踪**：集成 Sleuth 和 Zipkin
4. **监控集成**：添加 Micrometer 和 Prometheus
5. **配置中心**：集成 Spring Cloud Config
6. **断路器模式**：添加 Resilience4j 集成

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个学习项目！

## 许可证

MIT License
```
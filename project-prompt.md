好的，这是一个非常详细的 Prompt，你可以直接将其交给一个代码生成AI（如 ChatGPT-4, Copilot Chat 等）来快速创建一个功能丰富的、模拟真实工作场景的 Spring Cloud Gateway 学习工程。

这个 Prompt 的设计目标是：
1.  **结构清晰**：明确定义了项目结构和各个模块的职责。
2.  **场景真实**：涵盖了认证、日志、请求修改、错误处理、限流等高频网关需求。
3.  **代码可学**：生成的代码应该是模块化的、有注释的，便于你逐个功能进行学习和调试。
4.  **可运行**：包含 `docker-compose` 配置，让你一键启动整个微服务环境。

---

### **Prompt for Generating a Spring Cloud Gateway Learning Project**

**Role:** You are an expert senior Java developer specializing in Spring Cloud and microservices architecture. Your task is to generate a complete, runnable, and well-documented learning project that demonstrates the core functionalities of Spring Cloud Gateway in a realistic microservices setup.

**Project Overview:**

Create a multi-module Maven project named `spring-cloud-gateway-learning`. The project will simulate a common microservices ecosystem consisting of:

1.  `api-gateway`: The core Spring Cloud Gateway application.
2.  `user-service`: A simple downstream service providing user information.
3.  `product-service`: Another downstream service providing product details.
4.  `eureka-server`: A service discovery server (optional, but recommended for a complete setup).

The primary goal is to showcase various real-world scenarios handled by the `api-gateway`.

---

**Detailed Instructions:**

**1. Project Structure (Maven Multi-Module):**

Generate the following directory structure and `pom.xml` files. Use the latest stable Spring Boot (e.g., 3.x) and Spring Cloud versions.

```
spring-cloud-gateway-learning/
├── pom.xml                   (Parent POM)
├── api-gateway/
│   ├── src/main/java/...
│   └── src/main/resources/
├── user-service/
│   ├── src/main/java/...
│   └── src/main/resources/
├── product-service/
│   ├── src/main/java/...
│   └── src/main/resources/
├── eureka-server/
│   ├── src/main/java/...
│   └── src/main/resources/
└── docker-compose.yml        (For running the entire stack)
```

**2. `eureka-server` Module:**

*   **Dependencies:** `spring-cloud-starter-netflix-eureka-server`.
*   **Main Class:** Annotate with `@EnableEurekaServer`.
*   **`application.yml`:** Configure it to run on port `8761` and disable self-registration.

**3. `user-service` Module:**

*   **Dependencies:** `spring-boot-starter-web`, `spring-cloud-starter-netflix-eureka-client`, `lombok`.
*   **`application.yml`:**
    *   Set `server.port=8081` and `spring.application.name=user-service`.
    *   Configure Eureka client to register with the Eureka server.
*   **Controller:** Create a `UserController` with two endpoints:
    *   `GET /users/{id}`: Returns a mock `User` object (e.g., `{ "id": id, "name": "John Doe", "email": "<EMAIL>" }`).
    *   `POST /users/auth`: A protected endpoint that expects a header `X-User-Id`. It should return a success message like `"Authenticated access for user: {X-User-Id}"`.

**4. `product-service` Module:**

*   **Dependencies:** `spring-boot-starter-web`, `spring-cloud-starter-netflix-eureka-client`, `lombok`.
*   **`application.yml`:**
    *   Set `server.port=8082` and `spring.application.name=product-service`.
    *   Configure Eureka client.
*   **Controller:** Create a `ProductController` with one endpoint:
    *   `GET /products/{id}`: Returns a mock `Product` object (e.g., `{ "id": id, "name": "Awesome Gadget", "price": 99.99 }`).

**5. `api-gateway` Module (The Core Learning Part):**

*   **Dependencies:** `spring-cloud-starter-gateway`, `spring-cloud-starter-netflix-eureka-client`, `spring-boot-starter-data-redis-reactive` (for rate limiting).
*   **Main Class:** Standard Spring Boot application.
*   **`application.yml` Configuration:**
    *   Set `server.port=8080` and `spring.application.name=api-gateway`.
    *   Configure Eureka client.
    *   **Define Routes:**
        *   A route for `user-service` forwarding requests from `/api/users/**` to `lb://user-service`, stripping the `/api` prefix.
        *   A route for `product-service` forwarding requests from `/api/products/**` to `lb://product-service`, stripping the `/api` prefix.
    *   **Configure Redis** for the rate limiter (e.g., `spring.data.redis.host=localhost`).

*   **Implement Custom Filters (Create a `filters` package):**

    *   **Scenario 1: Authentication (`AuthGlobalFilter.java`)**
        *   Implement a `GlobalFilter` with a high order (e.g., `Ordered.HIGHEST_PRECEDENCE`).
        *   **Logic:**
            *   Check for an `Authorization` header.
            *   If the header is missing or invalid (e.g., not "Bearer valid-token"), immediately return a `401 Unauthorized` status.
            *   If valid, extract a user ID from the token (mock this, e.g., decode "valid-token" to user "123").
            *   **Crucially**, add the extracted user ID to the request headers (e.g., `X-User-Id: 123`) before passing it down the chain. This demonstrates modifying requests for downstream services.
        *   Add comments explaining why `ThreadLocal` is not used and how context is passed.

    *   **Scenario 2: Request/Response Logging (`LoggingGlobalFilter.java`)**
        *   Implement another `GlobalFilter`.
        *   **Logic:**
            *   Log the incoming request method, path, and headers.
            *   **Challenge:** Implement a **safe** request body logging mechanism. Use the **Conditional Logging** strategy: only log the body if the `Content-Type` is JSON-like and `Content-Length` is below a certain threshold (e.g., 1MB). For other types or large bodies, log a "Skipped" message.
            *   Use the `ServerWebExchangeUtils.cacheRequestBody` utility to read the body.
            *   Log the response status code and headers after the filter chain completes (using the `.then()` operator).
        *   Add comments explaining the "body can be consumed only once" problem and how caching solves it.

    *   **Scenario 3: Adding a Common Response Header (`AddResponseHeaderGatewayFilterFactory.java`)**
        *   Implement a custom `AbstractGatewayFilterFactory`.
        *   **Logic:** This filter should add a static header to all outgoing responses, like `X-Gateway-Timestamp: <current_timestamp>`.
        *   Show how to configure this filter in `application.yml` for a specific route.

*   **Implement Global Error Handling (`JsonGlobalExceptionHandler.java`)**
    *   Implement `ErrorWebExceptionHandler`.
    *   **Logic:** Catch any unhandled exceptions from the gateway or downstream services (like `NotFoundException` or connection errors).
    *   Instead of the default Spring Boot error page, format a consistent JSON error response, e.g., `{ "timestamp": "...", "path": "...", "status": 500, "error": "Internal Server Error", "message": "..." }`.

*   **Demonstrate Built-in Filters in `application.yml`:**
    *   On the `product-service` route, add a `RequestRateLimiter` filter. Configure it to use the Redis-based implementation, allowing, for example, 5 requests per 10 seconds per user (use the `KeyResolver` to resolve by IP address or a user principal if available).
    *   On the `user-service` route, use the `StripPrefix` filter you've already configured. Add a `Retry` filter to retry failed requests (e.g., 3 times on `5xx` errors).

**6. `docker-compose.yml` File:**

*   Create a `docker-compose.yml` in the root directory.
*   Define services for:
    *   `eureka-server`
    *   `user-service`
    *   `product-service`
    *   `api-gateway`
    *   A `redis` service for the rate limiter.
*   Ensure services depend on each other correctly (e.g., gateway and services depend on eureka).
*   The Compose file should build the Docker images from the local Maven build artifacts (using `build: .` context and specifying Dockerfiles, or by pre-building JARs).

**Final Output Instructions:**

*   Generate all Java classes with proper annotations, imports, and detailed Javadoc-style comments explaining the purpose of each filter and its key implementation details.
*   Generate all `pom.xml` and `application.yml` files with clear configuration.
*   Provide a `README.md` file in the root directory with:
    *   A brief project description.
    *   Instructions on how to build and run the project using Maven and `docker-compose up`.
    *   A list of example `curl` commands to test each scenario:
        *   A successful request to the user service.
        *   An unauthorized request that gets blocked.
        *   A request to the product service that demonstrates the rate limiter.
        *   A request to a non-existent endpoint to test the global error handler.

This comprehensive prompt will guide the AI to generate a project that is not just a "Hello World" but a true-to-life learning sandbox, perfect for a developer with existing microservices experience to quickly grasp the power and nuances of Spring Cloud Gateway.